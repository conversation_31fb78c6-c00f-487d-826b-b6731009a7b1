import { useSaasConfig } from "../../context/SaasConfigContext";

export const useSaasHelpers = () => {
  const {
    saasConfig,
    isSaaS,
    saasHostToken,
    saasMeetingFeatures,
    saasMeetingId,
  } = useSaasConfig();

  // Define functions that can reference each other
  const saasBrandingEnabled = () => {
    if (!isSaaS) return false;
    return (
      isSaaS &&
      saasMeetingFeatures?.configurations &&
      saasMeetingFeatures?.configurations?.branding_enabled === 1
    );
  };

  const saasMeetingConfigurations = () => {
    if (!saasBrandingEnabled()) return null;
    return saasMeetingFeatures?.configurations;
  };

  return {
    isSaaS,
    saasHostToken,
    saasMeetingFeatures,
    saasConfig,
    saasMeetingId,
    saasBrandingEnabled,
    saasMeetingConfigurations,
  };
};

